"""
Author: <PERSON><PERSON><PERSON><PERSON>
Email:  <EMAIL>
Date:   2022-04-08
"""


from quant.entrance import *


class Strategy:

	def __init__(self):
		
		self.rest_api = RestApi(platform=const.BINANCE_SWAP, symbol="BTC/USDT")
		self.do_action()
		
		# Event(
		# 	platform=const.OKX_SWAP,
		# 	channels=[const.ASSET],
		# 	symbols=["BTC/USDT"],
		# 	ticker_update_callback=self.on_event_ticker_update_callback,
		# 	trade_update_callback=self.on_event_trade_update_callback,
		# 	orderbook_update_callback=self.on_event_orderbook_update_callback,
		# 	kline_update_callback=self.on_event_kline_update_callback,
		# 	order_update_callback=self.on_event_order_update_callback,
		# 	position_update_callback=self.on_event_position_update_callback,
		# 	asset_update_callback=self.on_event_asset_update_callback
		# )
		
	def do_action(self):
		asset, error = self.rest_api.asset(currency="USDT")
		if error:
			logger.error("error:", error, caller=self)
			return
		logger.info("asset:", asset, caller=self)
		
	def on_event_ticker_update_callback(self, ticker: Ticker):
		logger.info("ticker:", ticker, caller=self)

	def on_event_trade_update_callback(self, trade: Trade):
		logger.info("trade:", trade, caller=self)

	def on_event_orderbook_update_callback(self, orderbook: Orderbook):
		logger.info("orderbook:", orderbook, caller=self)

	def on_event_kline_update_callback(self, kline: Kline):
		logger.info("kline:", kline, caller=self)

	def on_event_order_update_callback(self, order: Order):
		logger.info("order:", order, caller=self)

	def on_event_asset_update_callback(self, asset: Asset):
		logger.info("asset:", asset, caller=self)

	def on_event_position_update_callback(self, position: Position):
		logger.info("position:", position, caller=self)


if __name__ == '__main__':

	Quant.start("config.json", Strategy)